package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext

class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {
    
    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // ������ȫ��
        val lookupElement = LookupElementBuilder.create("wxb")
            .withPresentableText("wxb")
            .withTypeText("String")
            .withIcon(null)
            .withInsertHandler { insertionContext, _ ->
                // ������������Ӳ����Ĵ����߼�
                // ���磺�ƶ����λ�õ�
            }
        
        // ��ӵ���ȫ�����
        result.addElement(lookupElement)
    }
}

class WxbCompletionContributor : CompletionContributor() {
    
    init {
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}
