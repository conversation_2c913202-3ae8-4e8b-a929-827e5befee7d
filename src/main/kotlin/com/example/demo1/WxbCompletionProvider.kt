package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext

/**
 * Provides "wxb" string completion functionality
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        // Create completion item
        val lookupElement = LookupElementBuilder.create("wxb")
            .withPresentableText("wxb")
            .withTypeText("String")
            .withIcon(null)
            .withInsertHandler { insertionContext, _ ->
                // Additional logic can be added here if needed
                // For example: cursor positioning, etc.
            }

        // Add to completion results
        result.addElement(lookupElement)
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    init {
        // Provide completion in all contexts
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )
    }
}
