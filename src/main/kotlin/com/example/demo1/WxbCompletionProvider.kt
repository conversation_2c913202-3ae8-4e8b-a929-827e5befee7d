package com.example.demo1

import com.intellij.codeInsight.completion.*
import com.intellij.codeInsight.lookup.LookupElementBuilder
import com.intellij.patterns.PlatformPatterns
import com.intellij.util.ProcessingContext
import com.intellij.openapi.diagnostic.Logger

/**
 * Provides "wxb" string completion functionality
 */
class WxbCompletionProvider : CompletionProvider<CompletionParameters>() {

    private val logger = Logger.getInstance(WxbCompletionProvider::class.java)

    override fun addCompletions(
        parameters: CompletionParameters,
        context: ProcessingContext,
        result: CompletionResultSet
    ) {
        logger.info("WxbCompletionProvider.addCompletions called")

        // Create completion item
        val lookupElement = LookupElementBuilder.create("wxb")
            .withPresentableText("wxb")
            .withTypeText("String")
            .withIcon(null)
            .withInsertHandler { insertionContext, _ ->
                logger.info("wxb completion item inserted")
            }

        // Add to completion results
        result.addElement(lookupElement)
        logger.info("wxb completion item added to results")
    }
}

/**
 * Completion contributor that registers the completion provider
 */
class WxbCompletionContributor : CompletionContributor() {

    private val logger = Logger.getInstance(WxbCompletionContributor::class.java)

    init {
        logger.info("WxbCompletionContributor initialized")

        // Provide completion in all contexts
        extend(
            CompletionType.BASIC,
            PlatformPatterns.psiElement(),
            WxbCompletionProvider()
        )

        logger.info("WxbCompletionProvider registered")
    }
}
